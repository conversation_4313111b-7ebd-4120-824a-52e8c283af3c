// Generated by view binder compiler. Do not edit!
package com.yancao.qrscanner.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.yancao.qrscanner.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class TestBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final PreviewView overlayPreviewView;

  private TestBinding(@NonNull FrameLayout rootView, @NonNull PreviewView overlayPreviewView) {
    this.rootView = rootView;
    this.overlayPreviewView = overlayPreviewView;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static TestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static TestBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static TestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.overlay_preview_view;
      PreviewView overlayPreviewView = ViewBindings.findChildViewById(rootView, id);
      if (overlayPreviewView == null) {
        break missingId;
      }

      return new TestBinding((FrameLayout) rootView, overlayPreviewView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
