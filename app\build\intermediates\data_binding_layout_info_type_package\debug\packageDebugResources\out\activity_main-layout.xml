<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.yancao.qrscanner" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="150" endOffset="51"/></Target><Target id="@+id/preview_view" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="9" startOffset="4" endLine="30" endOffset="38"/></Target><Target id="@+id/tv_scan_results" view="TextView"><Expressions/><location startLine="18" startOffset="8" endLine="29" endOffset="37"/></Target><Target id="@+id/qr_overlay_view" view="com.yancao.qrscanner.ui.QrCodeOverlayView"><Expressions/><location startLine="33" startOffset="4" endLine="40" endOffset="62"/></Target><Target id="@+id/btn_flashlight" view="Button"><Expressions/><location startLine="45" startOffset="4" endLine="56" endOffset="62"/></Target><Target id="@+id/zoom_control_panel" view="FrameLayout"><Expressions/><location startLine="59" startOffset="4" endLine="102" endOffset="17"/></Target><Target id="@+id/zoom_slider_container" view="LinearLayout"><Expressions/><location startLine="69" startOffset="8" endLine="100" endOffset="22"/></Target><Target id="@+id/tv_zoom_ratio" view="TextView"><Expressions/><location startLine="79" startOffset="12" endLine="88" endOffset="51"/></Target><Target id="@+id/seek_bar_zoom" view="SeekBar"><Expressions/><location startLine="91" startOffset="12" endLine="98" endOffset="58"/></Target><Target id="@+id/control_panel" view="LinearLayout"><Expressions/><location startLine="105" startOffset="4" endLine="148" endOffset="18"/></Target><Target id="@+id/button_container" view="LinearLayout"><Expressions/><location startLine="117" startOffset="8" endLine="146" endOffset="22"/></Target><Target id="@+id/btn_scan" view="Button"><Expressions/><location startLine="125" startOffset="12" endLine="133" endOffset="58"/></Target><Target id="@+id/btn_broadcast" view="Button"><Expressions/><location startLine="136" startOffset="12" endLine="144" endOffset="58"/></Target></Targets></Layout>