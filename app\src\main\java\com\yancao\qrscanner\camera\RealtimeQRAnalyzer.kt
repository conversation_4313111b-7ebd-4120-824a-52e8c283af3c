package com.yancao.qrscanner.camera

import android.util.Log
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import com.yancao.qrscanner.domain.ScanResultsHolder
/**
 * 实时二维码分析器
 * 这个类用于实时分析摄像头预览中的二维码，并将结果存储到全局结果数组中
 *
 * 设计说明：
 * - 继承 ImageAnalysis.Analyzer 接口，用于 CameraX 的图像分析
 * - 使用 MLKit 进行二维码识别
 * - 自动将识别结果存储到 ScanResultsHolder 中
 * - 支持回调函数，通知外部识别结果
 */
@ExperimentalGetImage
class RealtimeQRAnalyzer(
    private val onQRCodeDetected: ((List<String>) -> Unit)? = null,
    // 直接传递Barcode对象，让View层处理坐标转换
    private val onQRCodeWithPosition: ((List<Barcode>, Int, Int) -> Unit)? = null
) : ImageAnalysis.Analyzer {

    // MLKit 二维码扫描器配置
    private val options = BarcodeScannerOptions.Builder()
        .setBarcodeFormats(Barcode.FORMAT_QR_CODE) // 只识别二维码
        .build()

    private val scanner = BarcodeScanning.getClient(options)

    // 用于控制扫描频率，避免过于频繁的扫描
    private var lastAnalyzedTimestamp = 0L
    private val analyzeInterval = 500L // 0.5秒扫描一次

    /**
     * 分析图像的核心方法
     * 这个方法会被 CameraX 自动调用，传入摄像头的每一帧图像
     */
    override fun analyze(imageProxy: ImageProxy) {
        val currentTimestamp = System.currentTimeMillis()

        if (currentTimestamp - lastAnalyzedTimestamp < analyzeInterval) {
            imageProxy.close()
            return
        }

        lastAnalyzedTimestamp = currentTimestamp

        val mediaImage = imageProxy.image
        if (mediaImage != null) {
            val image = InputImage.fromMediaImage(
                mediaImage,
                imageProxy.imageInfo.rotationDegrees
            )

            /**
             * 应该是CameraX没有把摄像头的横屏分辨率转换为竖屏分辨率，即1920*1080没有改成1080*1920
             * 需要针对CameraX代码进一步修改改成可以自动转换的
             */
            val imageWidth = mediaImage.height
            val imageHeight = mediaImage.width

            scanner.process(image)
                .addOnSuccessListener { barcodes ->
                    if (barcodes.isNotEmpty()) {
                        val results = barcodes.mapNotNull { it.rawValue }
                        ScanResultsHolder.addScanResults(results)
                        onQRCodeDetected?.invoke(results)

                        // 直接传递Barcode对象
                        onQRCodeWithPosition?.invoke(barcodes, imageWidth, imageHeight)
                    } else {
                        // 没有检测到二维码时传递空列表
                        onQRCodeWithPosition?.invoke(emptyList(), imageWidth, imageHeight)
                    }
                }
                .addOnFailureListener { exception ->
                    println("实时二维码扫描失败: ${exception.message}")
                    onQRCodeWithPosition?.invoke(emptyList(), imageWidth, imageHeight)
                }
                .addOnCompleteListener {
                    imageProxy.close()
                }
        } else {
            imageProxy.close()
        }
    }
}